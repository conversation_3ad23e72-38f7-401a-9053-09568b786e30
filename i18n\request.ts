import { getRequestConfig } from 'next-intl/server';
import { getMessages, locales, defaultLocale, isValidLocale } from '../lib/i18n';

export default getRequestConfig(async ({ locale }) => {
  // Validate and provide fallback for locale
  const validLocale = locale && isValidLocale(locale) ? locale : defaultLocale;

  console.log('i18n/request.ts - Received locale:', locale, 'Using locale:', validLocale);

  try {
    const messages = await getMessages(validLocale);
    return {
      locale: validLocale,
      messages
    };
  } catch (error) {
    console.error('Error in getRequestConfig:', error);
    // Fallback to default locale messages
    const fallbackMessages = await getMessages(defaultLocale);
    return {
      locale: defaultLocale,
      messages: fallbackMessages
    };
  }
});
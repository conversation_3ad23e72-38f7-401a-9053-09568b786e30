import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { isValidLocale } from '@/lib/i18n'
import CasesClient from './components/CasesClient'

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://0dot.com';

  return {
    title: isEnglish
      ? 'Success Stories - 0dot | Real Cases of Enterprise AI & Cloud Solutions'
      : '成功案例 - 0dot | 企业级AI与云计算解决方案真实案例',
    description: isEnglish
      ? 'Explore real success stories of 0dot\'s enterprise AI and cloud computing solutions. See how we helped leading companies achieve digital transformation with AI annotation, CPU rental, and education management systems.'
      : '探索0dot企业级AI与云计算解决方案的真实成功案例。了解我们如何通过AI智能标注、CPU算力租用和教育管理系统帮助知名企业实现数字化转型。',
    keywords: isEnglish
      ? ['0dot success stories', 'enterprise AI cases', 'cloud computing success', 'AI annotation projects', 'CPU rental cases', 'education management success', 'digital transformation cases', 'client testimonials', 'project results', 'business solutions']
      : ['0dot成功案例', '企业AI案例', '云计算成功案例', 'AI标注项目', 'CPU租用案例', '教育管理成功案例', '数字化转型案例', '客户见证', '项目成果', '商业解决方案'],
    openGraph: {
      title: isEnglish
        ? 'Success Stories - 0dot | Real Cases of Enterprise AI & Cloud Solutions'
        : '成功案例 - 0dot | 企业级AI与云计算解决方案真实案例',
      description: isEnglish
        ? 'Explore real success stories of 0dot\'s enterprise AI and cloud computing solutions. See how we helped leading companies achieve digital transformation.'
        : '探索0dot企业级AI与云计算解决方案的真实成功案例。了解我们如何帮助知名企业实现数字化转型。',
      url: `${baseUrl}/${locale}/cases`,
      siteName: '0dot',
      images: [
        {
          url: `${baseUrl}/og-cases.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? '0dot Success Stories' : '0dot成功案例',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: isEnglish
        ? 'Success Stories - 0dot | Real Cases of Enterprise AI & Cloud Solutions'
        : '成功案例 - 0dot | 企业级AI与云计算解决方案真实案例',
      description: isEnglish
        ? 'Explore real success stories of 0dot\'s enterprise AI and cloud computing solutions.'
        : '探索0dot企业级AI与云计算解决方案的真实成功案例。',
      images: [`${baseUrl}/og-cases.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/cases`,
      languages: {
        'zh-CN': `${baseUrl}/cases`,
        'en-US': `${baseUrl}/en/cases`,
      },
    },
  };
}

export default function Cases({ params: { locale } }: { params: { locale: string } }) {
  if (!isValidLocale(locale)) {
    notFound();
  }

  return <CasesClient />;
}
